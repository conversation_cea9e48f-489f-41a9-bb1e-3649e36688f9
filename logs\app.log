2025-07-08 12:02:26,583 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:26,665 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:26,665 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:26,673 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 12:02:31,942 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:31,942 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:31,942 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:31,946 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 12:02:33,512 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:33,580 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:33,580 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:33,580 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:33,584 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:34,080 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:34,080 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:34,084 - INFO - app - app.py:144 - Client selection: None, Plant: None, Type: None
2025-07-08 12:02:34,084 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 12:02:34,095 - INFO - app - app.py:345 - No client selected, showing welcome message
2025-07-08 12:02:36,532 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:36,532 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:36,532 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:36,532 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:36,532 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:36,729 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:36,729 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:36,742 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:02:36,744 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 12:02:36,748 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:02:36,751 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:36,950 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:37,237 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:37,435 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:02:37,435 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:38,356 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:02:39,738 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:02:39,897 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:02:40,064 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:02:40,231 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:02:40,382 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:02:40,382 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:41,682 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 12:02:44,638 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:44,638 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:44,638 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:44,638 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:44,638 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:44,900 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:44,922 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:44,922 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:02:44,926 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-01
2025-07-08 12:02:44,926 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:02:44,926 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:45,476 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:45,955 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:45,955 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:45,958 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:45,958 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:45,958 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:46,165 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:46,165 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:46,177 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:02:46,179 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 12:02:46,182 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:02:46,182 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:47,281 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:47,857 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:49,056 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:02:49,064 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:49,938 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:02:51,156 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:02:52,031 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:02:52,586 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:02:53,128 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:02:53,762 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:02:53,762 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:55,042 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 12:28:13,291 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:28:13,293 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:28:13,293 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:28:13,294 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:28:13,295 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:28:13,404 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:28:13,405 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:28:13,406 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:28:13,408 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 12:28:13,412 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:28:13,414 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:13,898 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:14,150 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:14,655 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:28:14,655 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:15,024 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:28:16,328 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:28:17,090 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:28:17,328 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:28:17,597 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:28:18,134 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:28:18,135 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:19,863 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 12:28:25,132 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:28:25,132 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:28:25,134 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:28:25,285 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:28:25,293 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:28:25,313 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:28:25,567 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:28:25,567 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:28:25,567 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:28:25,567 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 12:28:25,581 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:28:25,581 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:26,389 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:26,813 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:27,391 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:28:27,391 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:27,758 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:28:28,807 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:28:29,302 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:28:29,518 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:28:29,716 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:28:30,115 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:28:30,115 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:31,329 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 14:53:58,618 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:53:58,640 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:53:58,640 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:53:58,643 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 14:54:02,346 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:02,346 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:02,346 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:02,346 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 14:54:03,995 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:03,995 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:03,995 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:03,995 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:03,995 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:04,080 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:04,080 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:04,080 - INFO - app - app.py:144 - Client selection: None, Plant: None, Type: None
2025-07-08 14:54:04,095 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 14:54:04,101 - INFO - app - app.py:345 - No client selected, showing welcome message
2025-07-08 14:54:05,935 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:05,935 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:05,935 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:05,935 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:05,939 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:06,025 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:06,026 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:06,029 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 14:54:06,032 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 14:54:06,036 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 14:54:06,037 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:06,145 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:06,253 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:06,383 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 14:54:06,383 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:06,791 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 14:54:08,509 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 14:54:08,576 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 14:54:08,649 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 14:54:08,736 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 14:54:08,821 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 14:54:08,822 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:10,367 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 14:54:11,280 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:11,280 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:11,280 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:11,280 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:11,280 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:11,370 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:11,370 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:11,370 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 14:54:11,370 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-01
2025-07-08 14:54:11,380 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 14:54:11,380 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:11,772 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:11,943 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:12,179 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 14:54:12,181 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:12,232 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:12,233 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:12,233 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:12,235 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:12,235 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:12,343 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:12,344 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:12,347 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 14:54:12,348 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 14:54:12,352 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 14:54:12,353 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:13,034 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:13,330 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:14,221 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 14:54:15,922 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 14:54:16,647 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 14:54:16,879 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 14:54:17,104 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:19,300 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 17:24:23,366 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:24:23,454 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:24:23,454 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:24:23,454 - INFO - app - app.py:89 - User not authenticated, showing login form
